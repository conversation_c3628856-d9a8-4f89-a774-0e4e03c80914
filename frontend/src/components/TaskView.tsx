import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from './ui/button';
import { processTaskViewValue } from '../lib/utils';
import {
  <PERSON><PERSON>,
  ExternalLink,
  Loader2,
  ArrowLeft,
  Package,
  ChevronDown,
  X,
  Check,
  Download,
  Target,
  Building2,
  Sparkles,
  MessageSquare,
  Edit2,
  CheckSquare,
  Brain,
  TrendingUp,

  FileText
} from 'lucide-react';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// ✅ Updated interfaces based on the actual data structure
interface ValidationData {
  walmart_validation: string;
  llm_validation: string;
  final_validation: string;
  final_verdict: string;
}

interface CommentData {
  walmart_comment: string;
  llm_comment: string;
  final_comment: string;
}

interface SelectionData {
  final_value: string;
  source: string;
}

interface AttributeData {
  walmart_latest_value: string;
  llm_suggested_value: string;
  llm_suggested_url: string;
  values_from_sources: Record<string, string>; // Contains walmart, llm, brand, and all competitors
  competitor_values: Record<string, string>; // Only competitors
  validation_data: ValidationData;
  comment_data: CommentData;
  selection_data: SelectionData;
}




interface TaskData {
  item_id: string;
  user: string;
  timestamp: string;
  submission_id: string;
  walmart_url: string;
  walmart_latest_url: string;
  product_type: string;
  category: string;
  product_name: string;
  gtin: string;
  source_urls: Record<string, string>;
  product_attributes: Record<string, AttributeData>;
  competitor_summary?: {
    total_competitors: number;
    competitor_names: string[];
    competitor_data: Record<string, Record<string, string>>;
    competitor_urls: Record<string, string>;
  };
  data_summary?: {
    total_attributes: number;
    total_competitors: number;
    competitor_names: string[];
    has_competitor_data: boolean;
  };
  feedback_data?: {
    feedback: string;
    short_summary: string;
    detailed_summary: string;
    feedback_timestamp: string;
    request_id: string;
  };
  storage_metadata?: any;
  processing_metadata?: any;
  saved_at?: string;
  bucket_path?: string;
  bucket_name?: string;
}

// âœ… FIXED: Add proper interface for table row with index signature
interface TableRow {
  Attribute: string;
  Walmart: string;
  WalmartLatest: string;
  LLM: string;
  Brand: string;
  [key: string]: string; // âœ… This allows dynamic competitor columns
}

// âœ… ADD THESE NEW INTERFACES after your existing ones
interface SaveTableRowData {
  attribute: string;
  walmart: string;
  llm: string;
  brand: string;
  final_value: string;
  final_source: string;
  final_verdict: string;
  selected: boolean;
  competitor_values: Record<string, string>;
  validation_data: Record<string, string>;
  comment_data: Record<string, string>;
}

interface SaveTaskDataRequest {
  item_id: string;
  product_name: string;
  category: string;
  table_data: SaveTableRowData[];
  insights_data: {
    short_summary: string;
    detailed_summary: string;
  };
  selected_cells: string[];
  save_to_sheets: boolean;
}

export default function TaskView() {
  const { id } = useParams<{ id: string }>();
  const [taskData, setTaskData] = useState<TaskData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);
  const [selectedCells, setSelectedCells] = useState<Set<string>>(new Set());
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [insightsGenerated, setInsightsGenerated] = useState(false);
  const [shortSummary, setShortSummary] = useState('');
  const [detailedSummary, setDetailedSummary] = useState('');
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [feedback, setFeedback] = useState<string>("");
  const [feedbackSaving, setFeedbackSaving] = useState(false);


  const navigate = useNavigate();

  // ✅ Editable Walmart values state (matching response table functionality)
  const [editableWalmartValues, setEditableWalmartValues] = useState<Record<number, string>>({});
  const [originalWalmartValues, setOriginalWalmartValues] = useState<Record<number, string>>({});
  const [editingCell, setEditingCell] = useState<number | null>(null);

  // ✅ Validation and comment states (matching response table)
  const [walmartValidation, setWalmartValidation] = useState<Record<number, string>>({});
  const [llmValidation, setLlmValidation] = useState<Record<number, string>>({});
  const [walmartComments, setWalmartComments] = useState<Record<number, string>>({});
  const [llmComments, setLlmComments] = useState<Record<number, string>>({});
  const [showInsights, setShowInsights] = useState(false);

  // Keep this existing state - NO CHANGES
  const [walmartLatestValidation, setWalmartLatestValidation] = useState<Record<number, string>>({});

  // Add only this new state for comments
  const [walmartLatestCommentsOnly, setWalmartLatestCommentsOnly] = useState<Record<number, string>>({});


  // âœ… Get all unique competitor names from the data
  const allCompetitors = taskData?.competitor_summary?.competitor_names ||
    Array.from(new Set(
      Object.values(taskData?.product_attributes || {})
        .flatMap(attr => Object.keys(attr.competitor_values || {}))
    ));

  useEffect(() => {
    fetchTaskData();
  }, [id]);

  // âœ… NEW: Auto-select cells based on final_value matching
useEffect(() => {
  if (taskData?.product_attributes && allCompetitors.length > 0) {
    const newSelected = new Set<string>();
    const newEditableWalmart: Record<number, string> = {};
    const newOriginalWalmart: Record<number, string> = {};
    const newLlmValues: Record<number, string> = {};
    const newWalmartValidation: Record<number, string> = {};
    const newLlmValidation: Record<number, string> = {};
    const newWalmartComments: Record<number, string> = {};
    const newLlmComments: Record<number, string> = {};
    const newWalmartLatestValidation: Record<number, string> = {};
    const newWalmartLatestComments: Record<number, string> = {};
    const newFinalValidations: Record<number, string> = {};
    const newFinalComments: Record<number, string> = {};

    // Convert taskData to table rows format for auto-selection
    const tableRows: TableRow[] = Object.entries(taskData.product_attributes).map(([attr, data]) => {
      const baseRow: TableRow = {
        Attribute: attr,
        Walmart: data.values_from_sources?.walmart || data.walmart_latest_value || '',
        WalmartLatest: data.walmart_latest_value || '',
        LLM: data.llm_suggested_value || '',
        Brand: data.values_from_sources?.brand || '',
      };

      allCompetitors.forEach(comp => {
        baseRow[comp] = data.competitor_values?.[comp] || data.values_from_sources?.[comp] || '';
      });

      return baseRow;
    });

    // Auto-select cells and initialize all states
    Object.entries(taskData.product_attributes).forEach(([, data], rowIdx) => {
      const finalValue = data.selection_data?.final_value;

      // Initialize all states
      const walmartValue = data.values_from_sources?.walmart || data.walmart_latest_value || '';
      newEditableWalmart[rowIdx] = walmartValue;
      newOriginalWalmart[rowIdx] = walmartValue;
      newLlmValues[rowIdx] = data.llm_suggested_value || '';
      
      newWalmartValidation[rowIdx] = data.validation_data?.walmart_validation || '';
      newLlmValidation[rowIdx] = data.validation_data?.llm_validation || '';
      newWalmartComments[rowIdx] = data.comment_data?.walmart_comment || '';
      newLlmComments[rowIdx] = data.comment_data?.llm_comment || '';
      newWalmartLatestValidation[rowIdx] = '';
      newWalmartLatestComments[rowIdx] = '';
      
      newFinalValidations[rowIdx] = data.validation_data?.final_validation || '';
      newFinalComments[rowIdx] = data.comment_data?.final_comment || '';

      // Auto-selection logic (same as before)
      if (finalValue && finalValue !== '-' && finalValue !== 'NULL') {
        const row = tableRows[rowIdx];
        const columnsToCheck = ['Walmart', 'WalmartLatest', 'LLM', 'Brand', ...allCompetitors];

        columnsToCheck.forEach(column => {
          const cellValue = row[column];
          if (cellValue && cellValue !== '-' && cellValue !== 'NULL') {
            const isExactMatch = cellValue.trim() === finalValue.trim();
            const isSubstringMatch = finalValue.includes(cellValue.trim()) || cellValue.includes(finalValue.trim());
            const isCommaListMatch = finalValue.includes(',') && cellValue.includes(',') &&
              finalValue.split(',').some(part => cellValue.includes(part.trim()));

            if (isExactMatch || isSubstringMatch || isCommaListMatch) {
              const cellKey = `${rowIdx}-${column}`;
              newSelected.add(cellKey);
            }
          }
        });
      }
    });

    // Set all states
    setSelectedCells(newSelected);
    setEditableWalmartValues(newEditableWalmart);
    setOriginalWalmartValues(newOriginalWalmart);
    setWalmartValidation(newWalmartValidation);
    setLlmValidation(newLlmValidation);
    setWalmartComments(newWalmartComments);
    setLlmComments(newLlmComments);
    setWalmartLatestValidation(newWalmartLatestValidation);
    setWalmartLatestCommentsOnly(newWalmartLatestComments);
  }

  // Initialize feedback data (same as before)
  if (taskData?.feedback_data) {
    setShortSummary(taskData.feedback_data.short_summary || '');
    setDetailedSummary(taskData.feedback_data.detailed_summary || '');
    setInsightsGenerated(!!(taskData.feedback_data.short_summary || taskData.feedback_data.detailed_summary));
    setFeedback(taskData.feedback_data.feedback || "");
  }
}, [taskData, allCompetitors]);
  const fetchTaskData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/task/${id}/view`);

      if (!response.ok) {
        throw new Error(response.status === 404 ? 'Task not found' : 'Failed to load task');
      }

      const result = await response.json();
      setTaskData(result.data || result);
    } catch (err: any) {
      setError(err.message || 'Failed to load task data');
    } finally {
      setLoading(false);
    }
  };

  const copyLink = async () => {
    const currentUrl = window.location.href;
    try {
      await navigator.clipboard.writeText(currentUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  const isURL = (text: string): boolean => {
    if (!text || text === '-' || text === 'NULL') return false;
    try {
      new URL(text);
      return true;
    } catch {
      return false;
    }
  };

  // âœ… Handle cell selection with single checkbox constraint for Brand to Final Validation columns
 const handleCellToggle = useCallback((rowIdx: number, col: string) => {
  const key = `${rowIdx}-${col}`;
  const updated = new Set(selectedCells);
  const constrainedColumns = ["Walmart", "WalmartLatest", "LLM", "Brand", ...allCompetitors];

  if (updated.has(key)) {
    updated.delete(key);

    // Clear validation and comments based on which column was deselected
    if (col === 'Walmart') {
      setWalmartValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setWalmartComments(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });
    } else if (col === 'WalmartLatest') {
      setWalmartLatestValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setWalmartLatestCommentsOnly(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });
    } else if (col === 'LLM') {
      setLlmValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setLlmComments(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });
    }
  } else {
    // Single selection constraint for main columns
    if (constrainedColumns.includes(col)) {
      constrainedColumns.forEach(constrainedCol => {
        const constrainedKey = `${rowIdx}-${constrainedCol}`;
        if (updated.has(constrainedKey)) {
          updated.delete(constrainedKey);
        }
      });

      // Clear all validation and comment states when switching columns
      setWalmartValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setWalmartComments(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });
      setWalmartLatestValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setWalmartLatestCommentsOnly(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });
      setLlmValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[rowIdx];
        return newValidation;
      });
      setLlmComments(prev => {
        const newComments = { ...prev };
        delete newComments[rowIdx];
        return newComments;
      });

    }



    updated.add(key);
  }

  setSelectedCells(updated);
}, [selectedCells, allCompetitors]);

  // âœ… Editable Walmart cell functions (matching response table)
  const saveEdit = () => {
    setEditingCell(null);
  };

  const cancelEdit = () => {
    if (editingCell !== null) {
      setEditableWalmartValues(prev => ({
        ...prev,
        [editingCell]: originalWalmartValues[editingCell] || ''
      }));
    }
    setEditingCell(null);
  };

  // âœ… Helper functions
  const cleanNaNValue = (value: any): string => {
    if (value === null || value === undefined || value === '' ||
      String(value).toLowerCase() === 'nan' ||
      String(value).toLowerCase() === 'null') {
      return '-';
    }
    return String(value);
  };

  // ✅ Helper function to check if a row has any selected cells
  const hasSelectedCellsInRow = (rowIdx: number): boolean => {
    return Array.from(selectedCells).some(cellKey => cellKey.startsWith(`${rowIdx}-`));
  };

  // âœ… ADD THESE NEW FUNCTIONS before your return statement

  const generateInsights = async (): Promise<void> => {
    setInsightsLoading(true);

    // âœ… Clear existing summaries when generating new ones
    setShortSummary('');
    setDetailedSummary('');
    setInsightsGenerated(false);

    try {
      if (!taskData || selectedCells.size === 0) {
        throw new Error('No cells selected for analysis');
      }

      const selectedData: any[] = [];
      const attributesWithSelections = new Set<number>();

      selectedCells.forEach(cellId => {
        const [rowIdxStr, column] = cellId.split('-');
        console.log(`Row index: ${rowIdxStr}, Column: ${column}`);
        const rowIdx = parseInt(rowIdxStr);
        attributesWithSelections.add(rowIdx);
      });

      Array.from(attributesWithSelections).forEach(rowIdx => {
        const attributeData = Object.values(taskData.product_attributes)[rowIdx];
        if (!attributeData) return;

        const selectedCols = Array.from(selectedCells)
          .filter(cellId => cellId.startsWith(`${rowIdx}-`))
          .map(cellId => cellId.split('-')[1]);

        selectedData.push({
          attribute: Object.keys(taskData.product_attributes)[rowIdx],
          selectedSources: selectedCols,
          walmartLatestValidation: walmartLatestValidation[rowIdx] || '',
          walmartLatestComments: walmartLatestCommentsOnly[rowIdx] || '',
          llmValidation: llmValidation[rowIdx] || '',
          llmComments: llmComments[rowIdx] || '',
          finalValue: attributeData.selection_data?.final_value || ''
        });
      });

      const requestPayload = {
        item_id: taskData.item_id,
        product_name: taskData.product_name || 'Unknown Product',
        category: taskData.category || 'Unknown Category',
        product_attributes: Object.fromEntries(
          selectedData.map(item => [item.attribute, item.finalValue || ''])
        ),
        site_summaries: selectedData,
        generate_summary: true,
        include_validations: true
      };



      const resp = await fetch(`${API_BASE_URL}/api/task/${id}/insights`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestPayload)
      });

      if (!resp.ok) {
        throw new Error(`Insights API failed`);
      }

      const apiResult = await resp.json();

      // âœ… REPLACE existing summaries with new generated ones
      const newShortSummary = apiResult.short_summary || 'Analysis completed successfully.';
      const newDetailedSummary = apiResult.detailed_summary || 'Detailed insights have been generated.';

      setShortSummary(newShortSummary);
      setDetailedSummary(newDetailedSummary);
      setInsightsGenerated(true);

      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
      notification.textContent = `âœ… New insights generated for ${selectedCells.size} selected cells! ${taskData?.feedback_data ? 'Previous insights replaced.' : ''}`;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 4000);

    } catch (error) {
      console.error('Insights generation failed:', error);

      const attributesCount = new Set(Array.from(selectedCells).map(c => c.split('-')[0])).size;

      const fallbackShort = `Analysis completed for ${selectedCells.size} selected data points across ${attributesCount} product attributes.`;
      const fallbackDetailed = `â€¢ Selection Analysis: ${selectedCells.size} data points analyzed\nâ€¢ Product Category: ${taskData?.category || 'General category'}`;

      setShortSummary(fallbackShort);
      setDetailedSummary(fallbackDetailed);
      setInsightsGenerated(true);

      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-yellow-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
      notification.textContent = `âš ï¸ Fallback insights generated for ${selectedCells.size} selected cells`;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 4000);
    } finally {
      setInsightsLoading(false);
    }
  };

  const saveFeedback = async () => {
    if (!taskData?.item_id) return;


    setFeedbackSaving(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/task/${taskData.item_id}/feedback`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          feedback: feedback,
          request_id: `feedback_${Date.now()}`,
          feedback_timestamp: new Date().toISOString()
        }),
      });

      if (!response.ok) throw new Error("Failed to save feedback");

      const notification = document.createElement("div");
      notification.className = "fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50";
      notification.textContent = "Feedback saved successfully!";
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 3000);

    } catch (error: any) {
      const notification = document.createElement("div");
      notification.className = "fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50";
      notification.innerHTML = `<div class="font-semibold">Save Failed</div><div class="text-sm mt-1">${error.message}</div>`;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 5000);
    } finally {
      setFeedbackSaving(false);
    }
  };

  const saveTaskData = async () => {
    setSaving(true);
    try {
      const tableRows: TableRow[] = Object.entries(taskData?.product_attributes || {}).map(([attr, data]) => {
        const baseRow: TableRow = {
          Attribute: attr,
          Walmart: data.values_from_sources?.walmart || data.walmart_latest_value || '',
          WalmartLatest: data.walmart_latest_value || '',
          LLM: data.llm_suggested_value || '',
          Brand: data.values_from_sources?.brand || '',
        };

        allCompetitors.forEach(comp => {
          baseRow[comp] = data.competitor_values?.[comp] || data.values_from_sources?.[comp] || '';
        });

        return baseRow;
      });

      const tableData: SaveTableRowData[] = tableRows.map((row, rowIdx) => {
        const attributeData = Object.values(taskData?.product_attributes || {})[rowIdx];

        return {
          attribute: row.Attribute,
          walmart: row.Walmart,
          llm: row.LLM,
          brand: row.Brand,
          final_value: attributeData?.selection_data?.final_value || '',
          final_source: attributeData?.selection_data?.source || '',
          final_verdict: attributeData?.validation_data?.final_verdict || '',
          selected: Array.from(selectedCells).some(cellId => cellId.startsWith(`${rowIdx}-`)),
          competitor_values: allCompetitors.reduce((acc, comp) => {
            acc[comp] = row[comp] || '';
            return acc;
          }, {} as Record<string, string>),
          validation_data: {
            walmart_validation: attributeData?.validation_data?.walmart_validation || '',
            walmart_latest_validation: walmartLatestValidation[rowIdx] || '',
            llm_validation: attributeData?.validation_data?.llm_validation || '',
            final_validation: attributeData?.validation_data?.final_validation || ''
          },
          comment_data: {
            walmart_comment: attributeData?.comment_data?.walmart_comment || '',
            walmart_latest_comment: walmartLatestCommentsOnly[rowIdx] || '',
            llm_comment: attributeData?.comment_data?.llm_comment || '',
            final_comment: attributeData?.comment_data?.final_comment || ''
          }
        };
      });

      const requestPayload: SaveTaskDataRequest = {
        item_id: taskData?.item_id || id || '',
        product_name: taskData?.product_name || `Task ${taskData?.item_id}`,
        category: taskData?.category || 'Unknown Category',
        table_data: tableData,
        insights_data: {
          short_summary: shortSummary,
          detailed_summary: detailedSummary
        },
        selected_cells: Array.from(selectedCells),
        save_to_sheets: true
      };

      const response = await fetch(`${API_BASE_URL}/api/task/${id}/save`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestPayload)
      });

      if (!response.ok) {
        throw new Error(`Save failed`);
      }

      const result = await response.json();
      setSaveSuccess(true);

      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-xl shadow-2xl z-50 max-w-sm';
      notification.innerHTML = `
      <div class="font-bold text-lg">âœ… Saved to Google Sheets!</div>
      <div class="text-sm mt-2">ðŸ“Š ${result.total_rows_saved} rows saved</div>
    `;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 5000);

      if (result.sheets_result?.spreadsheet_url) {
        setTimeout(() => {
          const linkNotification = document.createElement('div');
          linkNotification.className = 'fixed top-16 right-4 bg-blue-500 text-white px-6 py-4 rounded-xl shadow-lg z-50 max-w-sm';
          linkNotification.innerHTML = `
          <div class="font-semibold">ðŸ“‹ View in Google Sheets</div>
          <a href="${result.sheets_result.spreadsheet_url}" target="_blank" class="text-blue-200 hover:text-white underline text-sm block mt-2">
            Open Spreadsheet â†’
          </a>
        `;
          document.body.appendChild(linkNotification);
          setTimeout(() => document.body.removeChild(linkNotification), 8000);
        }, 2000);
      }

    } catch (error: any) {
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
      notification.innerHTML = `
      <div class="font-semibold">âŒ Save Failed</div>
      <div class="text-sm mt-1">${error.message}</div>
    `;
      document.body.appendChild(notification);
      setTimeout(() => document.body.removeChild(notification), 5000);
    } finally {
      setSaving(false);
      setTimeout(() => setSaveSuccess(false), 4000);
    }
  };

  const formatSummary = (summary: string, options: { mode: 'short' | 'list' } = { mode: 'short' }): string | string[] => {
    if (!summary) return options.mode === 'list' ? [] : '';

    if (options.mode === 'list') {
      return summary
        .split('\n')
        .filter(line => line.trim())
        .map(line => line.replace(/^[â€¢Â·\-*]\s*/, '').trim())
        .filter(line => line.length > 0);
    }

    return summary.replace(/\n/g, ' ').trim();
  };


  // âœ… NEW: Check if cell is auto-selected based on final_value matching
  const isCellAutoSelected = (rowIdx: number, column: string, cellValue: string): boolean => {
    console.log("ðŸ” isCellAutoSelected:", rowIdx, column, cellValue);
    if (!taskData?.product_attributes) return false;

    const attributeData = Object.values(taskData.product_attributes)[rowIdx];
    const finalValue = attributeData?.selection_data?.final_value;

    if (!finalValue || finalValue === '-' || finalValue === 'NULL') return false;
    if (!cellValue || cellValue === '-' || cellValue === 'NULL') return false;

    // Enhanced matching logic
    const isExactMatch = cellValue.trim() === finalValue.trim();
    const isSubstringMatch = finalValue.includes(cellValue.trim()) || cellValue.includes(finalValue.trim());
    const isCommaListMatch = finalValue.includes(',') && cellValue.includes(',') &&
      finalValue.split(',').some(part => cellValue.includes(part.trim()));

    return isExactMatch || isSubstringMatch || isCommaListMatch;
  };

  // âœ… FIXED: Validation configuration (matching response table)
  const getValidationConfig = (status: string) => {
    switch (status) {
      case 'Yes':
        return { icon: Check, color: 'text-green-600', bgColor: 'bg-green-100', label: 'Yes' };
      case 'No':
        return { icon: X, color: 'text-red-600', bgColor: 'bg-red-100', label: 'No' };
      default:
        return { icon: null, color: 'text-gray-400', bgColor: 'bg-gray-50', label: 'Select Status' };
    }
  };

  // âœ… Get column width (matching response table)
  const getColumnWidth = (columnName: string): string => {
    switch (columnName) {
      case 'Attribute':
        return 'w-48 min-w-[12rem]';
      case 'Walmart':
      case 'WalmartLatest':
      case 'LLM':
        return 'w-64 min-w-[16rem]';
      case 'Validate Walmart':
      case 'Validate LLM':
        return 'w-44 min-w-[11rem] max-w-[11rem]';
      case 'Prefilled Value Comments':
        return 'w-48 min-w-[12rem] max-w-[12rem]';
      case 'LLM Comment':
        return 'w-44 min-w-[11rem] max-w-[11rem]';
      case 'Final Validation':
        return 'w-32 min-w-[8rem]';
      case 'Final Comment':
        return 'w-52 min-w-[13rem]';
      case 'Final Value':
        return 'w-52 min-w-[13rem]';
      case 'Source':
        return 'w-96 min-w-[24rem]';
      case 'Final Verdict':
        return 'w-80 min-w-[20rem]';
      default:
        return 'w-72 min-w-[18rem]';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <Loader2 className="h-16 w-16 animate-spin mx-auto mb-6 text-blue-600" />
          <p className="text-xl text-gray-600 dark:text-gray-400 font-medium">Loading task data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6 bg-gradient-to-br from-red-50 to-orange-50 dark:from-gray-900 dark:to-red-900/20">
        <div className="text-center max-w-lg bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8">
          <div className="text-8xl mb-6 text-red-500">âš ï¸</div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-3">Task Not Found</h1>
          <p className="text-gray-600 dark:text-gray-400 mb-8 text-lg">{error}</p>
          <div className="flex gap-4 justify-center">
            <Button onClick={fetchTaskData} className="bg-blue-600 hover:bg-blue-700">
              Try Again
            </Button>
            <Button variant="outline" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!taskData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-6">
        <div className="text-center">
          <Package className="h-16 w-16 text-gray-300 mx-auto mb-6" />
          <h2 className="text-2xl font-bold text-gray-500 mb-3">No Task Data</h2>
          <p className="text-gray-400 mb-6">Unable to load task information.</p>
          <Button onClick={() => navigate(-1)}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // âœ… FIXED: Convert taskData to table rows format with proper typing
  const tableRows: TableRow[] = Object.entries(taskData.product_attributes || {}).map(([attr, data]) => {
    const baseRow: TableRow = {
      Attribute: attr,
      Walmart: data.values_from_sources?.walmart || data.walmart_latest_value || '',
      WalmartLatest: data.walmart_latest_value || '',
      LLM: data.llm_suggested_value || '',
      Brand: data.values_from_sources?.brand || '',
    };

    // Add competitor columns dynamically
    allCompetitors.forEach(comp => {
      baseRow[comp] = data.competitor_values?.[comp] || data.values_from_sources?.[comp] || '';
    });

    return baseRow;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-indigo-900/20 py-8">
      <div className="max-w-[2000px] mx-auto px-6 space-y-8">
        {/* Header */}
        <div className="bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-6">
              <Button
                variant="outline"
                onClick={() => navigate(-1)}
                className="flex items-center gap-3 px-6 py-3 rounded-xl"
              >
                <ArrowLeft className="h-5 w-5" />
                Back
              </Button>
              <div>
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  Task {taskData?.item_id}
                </h1>
                <div className="flex items-center gap-4 text-gray-500 dark:text-gray-400">
                  <span className="flex items-center gap-1">
                    <Package className="h-4 w-4" />
                    {taskData?.category || 'Unknown Category'}
                  </span>
                  <span className="flex items-center gap-1">
                    <Building2 className="h-4 w-4" />
                    {allCompetitors.length} Competitors
                  </span>
                </div>
              </div>
            </div>
            <div className="flex gap-3">
              {/* âœ… ADD THIS FEEDBACK BUTTON: */}
              <Button
                variant={showInsights ? "default" : "outline"}
                onClick={() => setShowInsights(!showInsights)}
                className="flex items-center gap-2"
              >
                <MessageSquare className="h-4 w-4" />
                {showInsights ? 'Hide' : 'Show'} Feedback
              </Button>

              <Button onClick={copyLink} variant="outline" className="px-6 py-3 rounded-xl">
                <Copy className="h-4 w-4 mr-2" />
                {copied ? 'Copied!' : 'Share'}
              </Button>
              <Button variant="outline" className="px-6 py-3 rounded-xl">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>

          </div>

          {/* âœ… Enhanced Selection Summary with Final Value Matching Info */}
          {selectedCells.size > 0 && (
            <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <span className="text-green-800 dark:text-green-200 font-medium">
                    {selectedCells.size} cell(s) auto-selected based on final_value matching
                  </span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedCells(new Set())}
                  className="text-green-600 border-green-300"
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* âœ… UPDATED: Complete table with all competitor columns */}
        <div className="overflow-auto max-h-[70vh] rounded-2xl border border-indigo-200 dark:border-indigo-700 shadow-xl bg-white dark:bg-gray-900 mb-6">
          <table className="text-sm table-auto min-w-[2200px] border-collapse">
            {/* âœ… FIXED: Header with all columns */}
            <thead>
              <tr>
                {/* Attribute - Sticky */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-56 min-w-[14rem] sticky top-0 left-0 z-40 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Attribute</div>
                </th>

                {/* Walmart */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-64 min-w-[16rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Walmart</div>
                </th>

                {/* Validate Walmart */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-44 min-w-[11rem] max-w-[11rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="flex items-center justify-center gap-1">
                    <CheckSquare className="w-4 h-4 text-green-600 dark:text-green-400" />
                    <span className="font-extrabold">Validate</span>
                  </div>
                </th>

                {/* Prefilled Value Comments */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-48 min-w-[12rem] max-w-[12rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Prefilled Value Comments</div>
                </th>

                {/* Walmart Latest */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-64 min-w-[16rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Walmart Latest</div>
                </th>

                {/* WALMART LATEST VALIDATION Column Header */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-44 min-w-[11rem] max-w-[11rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="flex items-center justify-center gap-1">
                    <CheckSquare className="w-4 h-4 text-green-600 dark:text-green-400" />
                    <span className="font-extrabold">WALMART LATEST VALIDATION</span>
                  </div>
                </th>

                {/* WALMART LATEST COMMENTS Column Header - NO CHANGE */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-48 min-w-[12rem] max-w-[12rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">WALMART LATEST COMMENTS</div>
                </th>


                {/* LLM */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-64 min-w-[16rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="flex items-center justify-center gap-1">
                    <Sparkles className="w-4 h-4 text-purple-600" />
                    <span className="font-extrabold">LLM</span>
                  </div>
                </th>

                {/* Validate LLM */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-44 min-w-[11rem] max-w-[11rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="flex items-center justify-center gap-1">
                    <CheckSquare className="w-4 h-4 text-green-600 dark:text-green-400" />
                    <span className="font-extrabold">Validate</span>
                  </div>
                </th>

                {/* LLM Comment */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-44 min-w-[11rem] max-w-[11rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">LLM Comment</div>
                </th>

                {/* Brand */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-72 min-w-[18rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Brand</div>
                </th>

                {/* âœ… ALL Competitor Columns: shockwarehouse, truckworksunlimited, buybrakes, shocksurplus, 806dc */}
                {allCompetitors.map((competitor) => (
                  <th key={competitor} className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-72 min-w-[18rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                    <div className="font-extrabold text-center break-words px-2 capitalize">{competitor}</div>
                  </th>
                ))}

                {/* Final columns */}
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-32 min-w-[8rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Final Validation</div>
                </th>
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-52 min-w-[13rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Final Comment</div>
                </th>
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-52 min-w-[13rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Final Value</div>
                </th>
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-96 min-w-[24rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">Source</div>
                </th>
                <th className="px-3 py-3 text-xs font-bold uppercase tracking-wide text-indigo-900 dark:text-indigo-100 border border-indigo-200 dark:border-indigo-600 text-center shadow-sm w-80 min-w-[20rem] sticky top-0 z-30 bg-gradient-to-b from-indigo-100 to-indigo-200 dark:from-indigo-700 dark:to-indigo-800">
                  <div className="font-extrabold text-center break-words px-2">
                    <div className="bg-violet-500/20 rounded-lg px-2 py-1 text-violet-900 dark:text-violet-100">Final Verdict</div>
                  </div>
                </th>
              </tr>
            </thead>

            {/* âœ… UPDATED: Body with ALL columns including auto-selection highlighting */}
            <tbody>
              {tableRows.map((row, rowIdx) => {
                const isWalmartSelected = selectedCells.has(`${rowIdx}-Walmart`);
                const isLLMSelected = selectedCells.has(`${rowIdx}-LLM`);
                const isEditing = editingCell === rowIdx;
                const currentValue = cleanNaNValue(editableWalmartValues[rowIdx]);
                const originalValue = cleanNaNValue(originalWalmartValues[rowIdx]);
                const hasEdited = currentValue !== '-' && currentValue !== originalValue;
                const isEmpty = currentValue === '-';



                return (
                  <tr
                    key={rowIdx}
                    className={`${rowIdx % 2 === 0 ? 'bg-indigo-50 dark:bg-indigo-950' : 'bg-white dark:bg-gray-900'} hover:bg-indigo-100 dark:hover:bg-indigo-800 transition-colors`}
                  >
                    {/* Attribute - Sticky with Final Value Badge */}
                    <td className="px-3 py-2 text-sm border border-indigo-100 dark:border-indigo-700 text-gray-800 dark:text-gray-200 align-middle sticky left-0 z-20 bg-inherit font-semibold w-56 min-w-[14rem]">
                      <div className="flex flex-col gap-2">
                        <div className="block text-sm font-semibold leading-relaxed break-words cursor-pointer"
                          style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4', hyphens: 'auto' }}
                          title={row.Attribute}>
                          {row.Attribute.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>

                      </div>
                    </td>

                    {/* Walmart - Editable */}
                    <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth('Walmart')} ${isWalmartSelected ? 'ring-4 ring-green-400 bg-green-200 dark:bg-green-800' :
                      isCellAutoSelected(rowIdx, 'Walmart', row.Walmart) ? 'ring-2 ring-green-300 bg-green-100 dark:bg-green-900/30' :
                        hasEdited ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30 ring-1 ring-yellow-300 dark:ring-yellow-600' :
                          !hasEdited && !isEmpty ? 'bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30' :
                            isEmpty ? 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/30 italic text-gray-400' : ''
                      }`}
                      onClick={() => {
                        if (!loading && !isEditing) {
                          handleCellToggle(rowIdx, 'Walmart');
                        }
                      }}>


                      <div className="flex items-start gap-2">
                        <input
                          type="checkbox"
                          checked={isWalmartSelected}
                          readOnly
                          className="mt-1 h-4 w-4 rounded border border-blue-400 text-blue-600 focus:ring-2 focus:ring-blue-500 cursor-pointer flex-shrink-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCellToggle(rowIdx, 'Walmart');
                          }}
                        />
                        <div className="relative flex-1 min-w-0">
                          {hasEdited && !isEditing && (
                            <div className="absolute -top-3 -right-3 z-10">
                              <div className="flex items-center gap-1 px-1 py-0.5 rounded-full bg-orange-500 text-white text-xs font-sans shadow-lg">
                                <Edit2 size={8} />
                                <span>EDITED</span>
                              </div>
                            </div>
                          )}

                          <button
                            type="button"
                            title="Edit Walmart value"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEditingCell(rowIdx);
                            }}
                            className={`absolute -bottom-3 -right-1 p-1 rounded-full bg-white dark:bg-gray-800 border ${hasEdited ? 'border-orange-400 text-orange-600' : 'border-blue-300 dark:border-blue-600'
                              } opacity-0 group-hover:opacity-100 hover:text-blue-600 hover:border-blue-500 hover:shadow-md transition-all duration-300 z-10`}
                          >
                            <Edit2 size={12} className={hasEdited ? 'text-orange-500' : 'text-blue-500'} />
                          </button>

                          {!isEditing ? (
                            <div className={`block text-left font-medium text-sm leading-relaxed pr-6 break-words ${hasEdited ? 'text-orange-800 dark:text-orange-300 font-semibold' :
                              isEmpty ? 'italic text-gray-400 dark:text-gray-500' : ''
                              }`}
                              style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                              title={currentValue}>
                              {processTaskViewValue(currentValue, row.Attribute)}
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <textarea
                                value={currentValue !== '-' ? currentValue : ''}
                                autoFocus
                                rows={4}
                                onChange={(e) => setEditableWalmartValues(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }))}
                                onBlur={() => saveEdit()}
                                onKeyDown={(e) => {
                                  if (e.key === 'Enter' && !e.shiftKey) {
                                    e.preventDefault();
                                    saveEdit();
                                  }
                                  if (e.key === 'Escape') {
                                    cancelEdit();
                                  }
                                }}
                                className="w-full border-2 border-orange-400 rounded px-2 py-1 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm resize-none"
                                placeholder="Enter Walmart value..."
                              />
                              {originalValue !== '-' && (
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  <div className="font-medium">Original:</div>
                                  <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs break-words">{originalValue}</div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* Validate Walmart */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 w-44 min-w-[11rem] max-w-[11rem]">
                      <div className="flex flex-col items-center gap-2">
                        {walmartValidation[rowIdx] && (
                          <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${getValidationConfig(walmartValidation[rowIdx]).bgColor} ${getValidationConfig(walmartValidation[rowIdx]).color}`}>
                            {getValidationConfig(walmartValidation[rowIdx]).icon &&
                              React.createElement(getValidationConfig(walmartValidation[rowIdx]).icon!, { className: "w-3 h-3" })}
                            <span className="text-xs font-semibold">{getValidationConfig(walmartValidation[rowIdx]).label}</span>
                          </div>
                        )}
                        <div className="relative">
                          <select
                            value={walmartValidation[rowIdx] || ''}
                            disabled={!isWalmartSelected}
                            onChange={(e) => {
                              if (isWalmartSelected) {
                                setWalmartValidation(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartSelected ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{isWalmartSelected ? 'Select...' : 'Select Walmart first'}</option>
                            {isWalmartSelected && (
                              <>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                        </div>
                        {!isWalmartSelected && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart cell to enable</div>
                        )}
                      </div>
                    </td>

                    {/* Prefilled Value Comments */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 w-48 min-w-[12rem] max-w-[12rem]">
                      <div className="flex flex-col items-center gap-2">
                        {walmartComments[rowIdx] && (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                            <MessageSquare className="w-3 h-3" />
                            <span className="text-xs font-semibold">{walmartComments[rowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                          </div>
                        )}
                        <div className="relative">
                          <select
                            value={walmartComments[rowIdx] || ''}
                            disabled={!isWalmartSelected}
                            onChange={(e) => {
                              if (isWalmartSelected) {
                                setWalmartComments(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isWalmartSelected ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{isWalmartSelected ? 'Select...' : 'Select Walmart first'}</option>
                            {isWalmartSelected && (
                              <>
                                <option value="curated-from-base">Curated from base data</option>
                                <option value="curated-from-walmart-latest">Curated from Walmart Latest</option>
                                <option value="walmart-has-different-value">Walmart has different value</option>
                                <option value="vpd">VPD</option>
                                <option value="validated">Validated</option>
                                <option value="unable-to-curate">Unable to curate</option>
                                <option value="value-not-provided">Value not provided</option>
                                <option value="not-acceptable-value">Not in acceptable value</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isWalmartSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                        </div>
                        {!isWalmartSelected && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart cell to enable</div>
                        )}
                      </div>
                    </td>

                    {/* Walmart Latest */}
                    <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 ${getColumnWidth('WalmartLatest')} ${selectedCells.has(`${rowIdx}-WalmartLatest`) ? 'ring-4 ring-green-400 bg-green-200 dark:bg-green-800' :
                      isCellAutoSelected(rowIdx, 'WalmartLatest', row.WalmartLatest) ? 'ring-2 ring-green-300 bg-green-100 dark:bg-green-900/30' :
                        row.WalmartLatest !== '-' ? 'bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-950/50 dark:to-teal-900/30' :
                          'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/30 italic text-gray-400'
                      }`}
                      onClick={() => {
                        if (!loading) {
                          handleCellToggle(rowIdx, 'WalmartLatest');
                        }
                      }}>
                      {/* Auto-selection indicator */}
                      {isCellAutoSelected(rowIdx, 'WalmartLatest', row.WalmartLatest) && (
                        <div className="absolute -top-2 -right-2 z-20">
                          <div className="bg-green-500 text-white rounded-full p-1 shadow-lg">
                            <Check className="w-3 h-3" />
                          </div>
                        </div>
                      )}

                      <div className="flex items-start gap-2">
                        <input
                          type="checkbox"
                          checked={selectedCells.has(`${rowIdx}-WalmartLatest`)}
                          readOnly
                          className="mt-1 h-4 w-4 rounded border border-teal-400 text-teal-600 focus:ring-2 focus:ring-teal-500 cursor-pointer flex-shrink-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCellToggle(rowIdx, 'WalmartLatest');
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          {isURL(row.WalmartLatest) && row.WalmartLatest !== '-' ? (
                            <a
                              href={row.WalmartLatest}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 hover:underline transition-colors inline-flex items-center gap-1 group"
                              onClick={(e) => e.stopPropagation()}
                              style={{ overflowWrap: 'anywhere', wordBreak: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                            >
                              <span className="flex-1 text-sm font-medium">{processTaskViewValue(row.WalmartLatest, row.Attribute)}</span>
                              <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                            </a>
                          ) : (
                            <div className={`block text-left font-medium text-sm leading-relaxed break-words ${row.WalmartLatest === '-' ? 'italic text-gray-400 dark:text-gray-500' : 'text-teal-800 dark:text-teal-200'
                              }`}
                              style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                              title={row.WalmartLatest}>
                              {processTaskViewValue(row.WalmartLatest, row.Attribute)}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* WALMART LATEST VALIDATION column - Uses existing walmartLatestValidation state */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 w-44 min-w-[11rem] max-w-[11rem]">
                      <div className="flex flex-col items-center gap-2">
                        {/* Validation Status Display */}
                        {walmartLatestValidation[rowIdx] && (
                          <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${getValidationConfig(walmartLatestValidation[rowIdx]).bgColor} ${getValidationConfig(walmartLatestValidation[rowIdx]).color}`}>
                            {getValidationConfig(walmartLatestValidation[rowIdx]).icon &&
                              React.createElement(getValidationConfig(walmartLatestValidation[rowIdx]).icon!, { className: 'w-3 h-3' })
                            }
                            <span className="text-xs font-semibold">{getValidationConfig(walmartLatestValidation[rowIdx]).label}</span>
                          </div>
                        )}

                        {/* Validation Dropdown */}
                        <div className="relative">
                          <select
                            value={walmartLatestValidation[rowIdx] || ''}
                            disabled={!selectedCells.has(`${rowIdx}-WalmartLatest`)}
                            onChange={(e) => {
                              if (selectedCells.has(`${rowIdx}-WalmartLatest`)) {
                                setWalmartLatestValidation(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${selectedCells.has(`${rowIdx}-WalmartLatest`) ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{selectedCells.has(`${rowIdx}-WalmartLatest`) ? 'Select...' : 'Select Walmart Latest first'}</option>
                            {selectedCells.has(`${rowIdx}-WalmartLatest`) && (
                              <>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${selectedCells.has(`${rowIdx}-WalmartLatest`) ? 'text-gray-400' : 'text-gray-300'
                            }`} />
                        </div>

                        {!selectedCells.has(`${rowIdx}-WalmartLatest`) && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart Latest cell to enable</div>
                        )}
                      </div>
                    </td>

                    {/* WALMART LATEST COMMENTS column - NO CHANGE */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/30 w-48 min-w-[12rem] max-w-[12rem]">
                      <div className="flex flex-col items-center gap-2">
                        {/* Comment Status Display */}
                        {walmartLatestCommentsOnly[rowIdx] && (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                            <MessageSquare className="w-3 h-3" />
                            <span className="text-xs font-semibold">{walmartLatestCommentsOnly[rowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                          </div>
                        )}

                        {/* Comments Dropdown */}
                        <div className="relative">
                          <select
                            value={walmartLatestCommentsOnly[rowIdx] || ''}
                            disabled={!selectedCells.has(`${rowIdx}-WalmartLatest`)}
                            onChange={(e) => {
                              if (selectedCells.has(`${rowIdx}-WalmartLatest`)) {
                                setWalmartLatestCommentsOnly(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${selectedCells.has(`${rowIdx}-WalmartLatest`) ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-blue-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{selectedCells.has(`${rowIdx}-WalmartLatest`) ? 'Select...' : 'Select Walmart Latest first'}</option>
                            {selectedCells.has(`${rowIdx}-WalmartLatest`) && (
                              <>
                                <option value="curated-from-latest">Curated from latest data</option>
                                <option value="latest-vpd">Latest VPD</option>
                                <option value="validated-latest">Validated Latest</option>
                                <option value="unable-to-curate-latest">Unable to curate latest</option>
                                <option value="latest-value-not-provided">Latest value not provided</option>
                                <option value="not-acceptable-latest-value">Not acceptable latest value</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${selectedCells.has(`${rowIdx}-WalmartLatest`) ? 'text-gray-400' : 'text-gray-300'
                            }`} />
                        </div>

                        {!selectedCells.has(`${rowIdx}-WalmartLatest`) && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select Walmart Latest cell to enable</div>
                        )}
                      </div>
                    </td>


                    {/* LLM */}
                    <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 cursor-pointer hover:shadow-md transition-all duration-200 ${getColumnWidth('LLM')} ${isLLMSelected ? 'ring-4 ring-green-400 bg-green-200 dark:bg-green-800' :
                      isCellAutoSelected(rowIdx, 'LLM', row.LLM) ? 'ring-2 ring-green-300 bg-green-100 dark:bg-green-900/30' : ''
                      }`}
                      onClick={() => handleCellToggle(rowIdx, 'LLM')}
                      title={row.LLM !== '-' ? row.LLM : 'Click to visit source'}>
                      {/* Auto-selection indicator */}
                      {isCellAutoSelected(rowIdx, 'LLM', row.LLM) && (
                        <div className="absolute -top-2 -right-2 z-20">
                          <div className="bg-green-500 text-white rounded-full p-1 shadow-lg">
                            <Check className="w-3 h-3" />
                          </div>
                        </div>
                      )}

                      <div className="flex items-start gap-2">
                        <input
                          type="checkbox"
                          checked={isLLMSelected}
                          readOnly
                          className="mt-1 h-4 w-4 rounded border border-purple-400 text-purple-600 focus:ring-2 focus:ring-purple-500 cursor-pointer flex-shrink-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCellToggle(rowIdx, 'LLM');
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          <div className={`block text-left font-medium text-sm leading-relaxed break-words ${row.LLM === '-' ? 'italic text-gray-400 dark:text-gray-500' : ''
                            }`}
                            style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                            {processTaskViewValue(row.LLM, row.Attribute)}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Validate LLM */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 w-44 min-w-[11rem] max-w-[11rem]">
                      <div className="flex flex-col items-center gap-2">
                        {llmValidation[rowIdx] && (
                          <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${getValidationConfig(llmValidation[rowIdx]).bgColor} ${getValidationConfig(llmValidation[rowIdx]).color}`}>
                            {getValidationConfig(llmValidation[rowIdx]).icon &&
                              React.createElement(getValidationConfig(llmValidation[rowIdx]).icon!, { className: "w-3 h-3" })}
                            <span className="text-xs font-semibold">{getValidationConfig(llmValidation[rowIdx]).label}</span>
                          </div>
                        )}
                        <div className="relative">
                          <select
                            value={llmValidation[rowIdx] || ''}
                            disabled={!isLLMSelected}
                            onChange={(e) => {
                              if (isLLMSelected) {
                                setLlmValidation(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isLLMSelected ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-purple-400 focus:ring-2 focus:ring-purple-500 focus:border-purple-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{isLLMSelected ? 'Select...' : 'Select LLM first'}</option>
                            {isLLMSelected && (
                              <>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isLLMSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                        </div>
                        {!isLLMSelected && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select LLM cell to enable</div>
                        )}
                      </div>
                    </td>

                    {/* LLM Comment */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-center align-middle bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/30 w-44 min-w-[11rem] max-w-[11rem]">
                      <div className="flex flex-col items-center gap-2">
                        {llmComments[rowIdx] && (
                          <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                            <MessageSquare className="w-3 h-3" />
                            <span className="text-xs font-semibold">{llmComments[rowIdx].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                          </div>
                        )}
                        <div className="relative">
                          <select
                            value={llmComments[rowIdx] || ''}
                            disabled={!isLLMSelected}
                            onChange={(e) => {
                              if (isLLMSelected) {
                                setLlmComments(prev => ({
                                  ...prev,
                                  [rowIdx]: e.target.value
                                }));
                              }
                            }}
                            className={`appearance-none border rounded-md px-2 py-1 pr-6 text-xs font-medium focus:outline-none cursor-pointer transition-colors ${isLLMSelected ?
                              'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 hover:border-purple-400 focus:ring-2 focus:ring-purple-500 focus:border-purple-500' :
                              'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                              }`}
                          >
                            <option value="">{isLLMSelected ? 'Select...' : 'Select LLM first'}</option>
                            {isLLMSelected && (
                              <>
                                <option value="value-found-from-llm">Value found from LLM</option>
                                <option value="llm-value-mismatch">LLM Value Mismatch</option>
                                <option value="no-value-found">No Value Found</option>
                              </>
                            )}
                          </select>
                          <ChevronDown className={`absolute right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 pointer-events-none ${isLLMSelected ? 'text-gray-400' : 'text-gray-300'}`} />
                        </div>
                        {!isLLMSelected && (
                          <div className="text-xs text-gray-400 dark:text-gray-500 text-center">Select LLM cell to enable</div>
                        )}
                      </div>
                    </td>

                    {/* Brand */}
                    <td className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 hover:shadow-md transition-all duration-200 ${getColumnWidth('Brand')} ${selectedCells.has(`${rowIdx}-Brand`) ? 'ring-4 ring-green-400 bg-green-200 dark:bg-green-800' :
                      isCellAutoSelected(rowIdx, 'Brand', row.Brand) ? 'ring-2 ring-green-300 bg-green-100 dark:bg-green-900/30' : ''
                      }`}
                      onClick={() => handleCellToggle(rowIdx, 'Brand')}
                      title={row.Brand}>
                      {/* Auto-selection indicator */}
                      {isCellAutoSelected(rowIdx, 'Brand', row.Brand) && (
                        <div className="absolute -top-2 -right-2 z-20">
                          <div className="bg-green-500 text-white rounded-full p-1 shadow-lg">
                            <Check className="w-3 h-3" />
                          </div>
                        </div>
                      )}

                      <div className="flex items-start gap-2">
                        <input
                          type="checkbox"
                          checked={selectedCells.has(`${rowIdx}-Brand`)}
                          readOnly
                          className="mt-1 h-4 w-4 rounded border border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500 flex-shrink-0"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="block text-left text-sm leading-relaxed font-medium break-words"
                            style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                            {processTaskViewValue(row.Brand, row.Attribute)}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* âœ… ALL COMPETITOR COLUMNS: shockwarehouse, truckworksunlimited, buybrakes, shocksurplus, 806dc */}
                    {allCompetitors.map((competitor) => (
                      <td key={competitor} className={`border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 hover:shadow-md transition-all duration-200 ${getColumnWidth(competitor)} ${selectedCells.has(`${rowIdx}-${competitor}`) ? 'ring-4 ring-green-400 bg-green-200 dark:bg-green-800' :
                        isCellAutoSelected(rowIdx, competitor, row[competitor]) ? 'ring-2 ring-green-300 bg-green-100 dark:bg-green-900/30' : ''
                        }`}
                        onClick={() => handleCellToggle(rowIdx, competitor)}
                        title={row[competitor]}>
                        {/* âœ… Auto-selection indicator for each competitor */}
                        {isCellAutoSelected(rowIdx, competitor, row[competitor]) && (
                          <div className="absolute -top-2 -right-2 z-20">
                            <div className="bg-green-500 text-white rounded-full p-1 shadow-lg">
                              <Check className="w-3 h-3" />
                            </div>
                          </div>
                        )}

                        <div className="flex items-start gap-2">
                          <input
                            type="checkbox"
                            checked={selectedCells.has(`${rowIdx}-${competitor}`)}
                            readOnly
                            className="mt-1 h-4 w-4 rounded border border-gray-400 text-blue-600 focus:ring-2 focus:ring-blue-500 flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            {isURL(row[competitor]) && row[competitor] !== '-' ? (
                              <a
                                href={row[competitor]}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:underline transition-colors inline-flex items-center gap-1 group text-sm break-words"
                                onClick={(e) => e.stopPropagation()}
                                style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}
                              >
                                <span className="flex-1">{processTaskViewValue(row[competitor], row.Attribute)}</span>
                                <ExternalLink className="w-3 h-3 opacity-60 group-hover:opacity-100 flex-shrink-0" />
                              </a>
                            ) : (
                              <div className="block text-left text-sm leading-relaxed font-medium break-words"
                                style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                                {processTaskViewValue(row[competitor], row.Attribute)}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    ))}

                    {/* Final Validation - Shows combined validation from selected sources */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-900/30 font-medium w-32 min-w-[8rem]">
                      <div className="block text-sm break-words" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                        {(() => {
                          const validations = [];
                          if (walmartValidation[rowIdx]) validations.push(walmartValidation[rowIdx]);
                          if (walmartLatestValidation[rowIdx]) validations.push(walmartLatestValidation[rowIdx]);
                          if (llmValidation[rowIdx]) validations.push(llmValidation[rowIdx]);
                          return validations.length > 0 ? validations.join(', ') : '-';
                        })()}
                      </div>
                    </td>

                    {/* Final Comment - Shows combined comments from selected sources */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-950/50 dark:to-teal-900/30 font-medium w-52 min-w-[13rem]">
                      <div className="block text-sm break-words" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                        {(() => {
                          const formatComment = (comment: string) => {
                            if (!comment) return '';
                            return comment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                          };
                          const comments = [];
                          if (walmartComments[rowIdx]) comments.push(formatComment(walmartComments[rowIdx]));
                          if (walmartLatestCommentsOnly[rowIdx]) comments.push(formatComment(walmartLatestCommentsOnly[rowIdx]));
                          if (llmComments[rowIdx]) comments.push(formatComment(llmComments[rowIdx]));
                          return comments.length > 0 ? comments.join(' | ') : '-';
                        })()}
                      </div>
                    </td>

                    {/* âœ… Final Value column - Shows value from selected cell */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-center align-middle bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/30 font-medium w-52 min-w-[13rem] relative">
                      <div className="block text-sm break-words font-bold text-green-800 dark:text-green-200" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                        {(() => {
                          if (!hasSelectedCellsInRow(rowIdx)) return '-';

                          // Get value from selected cell
                          if (selectedCells.has(`${rowIdx}-Walmart`)) {
                            return processTaskViewValue(cleanNaNValue(editableWalmartValues[rowIdx] || row.Walmart), row.Attribute);
                          }
                          if (selectedCells.has(`${rowIdx}-WalmartLatest`)) {
                            return processTaskViewValue(cleanNaNValue(row.WalmartLatest), row.Attribute);
                          }
                          if (selectedCells.has(`${rowIdx}-LLM`)) {
                            return processTaskViewValue(cleanNaNValue(row.LLM), row.Attribute);
                          }
                          if (selectedCells.has(`${rowIdx}-Brand`)) {
                            return processTaskViewValue(cleanNaNValue(row.Brand), row.Attribute);
                          }

                          // Check competitors
                          for (const competitor of allCompetitors) {
                            if (selectedCells.has(`${rowIdx}-${competitor}`)) {
                              return processTaskViewValue(cleanNaNValue(row[competitor]), row.Attribute);
                            }
                          }

                          return '-';
                        })()}
                      </div>
                    </td>

                    {/* Source column - Shows source name based on selected cell */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 text-left align-middle bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/50 dark:to-amber-900/30 font-medium w-96 min-w-[24rem]">
                      <div className="block text-sm break-words font-medium text-amber-800 dark:text-amber-200" style={{ overflowWrap: 'anywhere', wordBreak: 'break-word', whiteSpace: 'normal', lineHeight: '1.4' }}>
                        {(() => {
                          if (!hasSelectedCellsInRow(rowIdx)) return '-';

                          // Get source name from selected cell
                          if (selectedCells.has(`${rowIdx}-Walmart`)) {
                            return 'Walmart';
                          }
                          if (selectedCells.has(`${rowIdx}-WalmartLatest`)) {
                            return 'Walmart Latest';
                          }
                          if (selectedCells.has(`${rowIdx}-LLM`)) {
                            return 'LLM';
                          }
                          if (selectedCells.has(`${rowIdx}-Brand`)) {
                            return 'Brand';
                          }

                          // Check competitors
                          for (const competitor of allCompetitors) {
                            if (selectedCells.has(`${rowIdx}-${competitor}`)) {
                              return competitor;
                            }
                          }

                          return '-';
                        })()}
                      </div>
                    </td>

                    {/* Final Verdict  */}
                    <td className="border border-gray-300 dark:border-gray-600 px-3 py-3 text-gray-800 dark:text-gray-200 align-top cursor-pointer relative group hover:shadow-md transition-all duration-200 w-80 min-w-[20rem] bg-gradient-to-br from-violet-50 to-violet-100 dark:from-violet-950/50 dark:to-violet-900/30">
                      <div className="text-sm leading-relaxed break-words text-violet-800 dark:text-violet-200 max-h-32 overflow-y-auto" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', whiteSpace: 'pre-wrap', lineHeight: '1.4' }}>
                        {hasSelectedCellsInRow(rowIdx)
                          ? processTaskViewValue(Object.values(taskData.product_attributes)[rowIdx]?.validation_data?.final_verdict || '-', row.Attribute)
                          : '-'
                        }
                        {(() => {
  const value = hasSelectedCellsInRow(rowIdx)
    ? processTaskViewValue(
        Object.values(taskData.product_attributes)[rowIdx]?.validation_data?.final_verdict || "-",
        row.Attribute
      )
    : "-";
  console.log("📦 Printing value:", value);
  return value;
})()}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Enhanced Selection Summary */}
        {selectedCells.size > 0 && (
          <div className="bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8">
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              Auto-Selected Cells ({selectedCells.size})
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Cells automatically selected based on matching final values from these columns:
              <span className="font-medium"> Walmart, Walmart Latest, LLM, Brand, {allCompetitors.join(', ')}</span>
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from(selectedCells).slice(0, 9).map((cellId) => (
                <div key={cellId} className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <code className="text-sm text-green-800 dark:text-green-200">{cellId}</code>
                  <div className="text-xs text-green-600 dark:text-green-400 mt-1">â€¢ Auto-matched</div>
                </div>
              ))}
              {selectedCells.size > 9 && (
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 flex items-center justify-center">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">
                    +{selectedCells.size - 9} more
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* AI Insights Section */}
        <div className="bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold mb-2 flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                  <Brain className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                AI Insights Generator
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Generate comprehensive product analysis and insights based on selected cells
              </p>
            </div>

            <Button
              onClick={generateInsights}
              disabled={insightsLoading || selectedCells.size === 0}
              className={`px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105 ${insightsLoading || selectedCells.size === 0
                ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-500 to-indigo-500 text-white hover:from-purple-600 hover:to-indigo-600 flex items-center gap-2'
                }`}
            >
              {insightsLoading ? (
                <>
                  <Loader2 className="h-5 w-5 animate-spin" />
                  Generating Insights...
                </>
              ) : (
                <>
                  <Brain className="h-5 w-5" />
                  Generate AI Insights ({selectedCells.size} cells)
                </>
              )}
            </Button>
          </div>

          {/* Loading State */}
          {insightsLoading && (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 px-6 py-3 bg-purple-50 dark:bg-purple-900/20 rounded-xl">
                <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
                <span className="text-purple-600 dark:text-purple-400 font-medium">
                  Analyzing {selectedCells.size} selected cells...
                </span>
              </div>
            </div>
          )}

          {/* Generated Insights Display */}
          {!insightsLoading && insightsGenerated && (shortSummary || detailedSummary) && (
            <div className="space-y-6 transition-all duration-500">
              {shortSummary && (
                <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200 dark:border-blue-700">
                  <h3 className="font-bold text-xl text-blue-800 dark:text-blue-300 mb-3 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Executive Summary
                    {taskData?.feedback_data && (
                      <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full ml-2">
                        {taskData.feedback_data.short_summary === shortSummary ? 'From Saved Data' : 'Newly Generated'}
                      </span>
                    )}
                  </h3>
                  <p className="text-gray-800 dark:text-gray-200 leading-relaxed text-lg">
                    {shortSummary}
                  </p>
                </div>
              )}

              {detailedSummary && (
                <div className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl border border-green-200 dark:border-green-700">
                  <h3 className="font-bold text-xl text-green-800 dark:text-green-300 mb-4 flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Detailed Analysis
                    {taskData?.feedback_data && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full ml-2">
                        {taskData.feedback_data.detailed_summary === detailedSummary ? 'From Saved Data' : 'Newly Generated'}
                      </span>
                    )}
                  </h3>

                  {/* Handle HTML content in detailed_summary */}
                  {detailedSummary.startsWith('<ul>') ? (
                    <div
                      className="text-gray-800 dark:text-gray-200 leading-relaxed space-y-2 prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: detailedSummary }}
                    />
                  ) : (
                    <ul className="space-y-3">
                      {(formatSummary(detailedSummary, { mode: 'list' }) as string[]).map((point, idx) => (
                        <li key={idx} className="flex items-start gap-3 text-gray-800 dark:text-gray-200">
                          <span className="text-green-600 mt-1 font-bold">â€¢</span>
                          <span className="leading-relaxed">{point}</span>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              )}
            </div>
          )}

          {/* No Selection Message */}
          {selectedCells.size === 0 && !insightsLoading && (
            <div className="text-center py-12 text-gray-500 dark:text-gray-400">
              <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No cells selected</p>
              <p>Select cells in the table above to generate AI insights</p>
            </div>
          )}
        </div>

        {/* Feedback Section */}
        <div className="bg-white/80 backdrop-blur-sm dark:bg-gray-800/80 rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/50 p-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold mb-2 flex items-center gap-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                  <MessageSquare className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                Task Feedback
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Provide your feedback on this task
              </p>
            </div>
            <Button
              onClick={saveFeedback}
              disabled={feedbackSaving || !feedback.trim()}
              className="px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105"
            >
              {feedbackSaving ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Save Feedback
                </>
              )}
            </Button>
          </div>

          <div className="space-y-4">
            {/* Current Feedback Display */}
            {taskData?.feedback_data?.feedback && (
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
                <div className="flex items-center gap-2 mb-2">
                  <MessageSquare className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                    Current Feedback
                  </span>
                  {taskData.feedback_data.feedback_timestamp && (
                    <span className="text-xs text-blue-600 dark:text-blue-400">
                      ({formatDate(taskData.feedback_data.feedback_timestamp)})
                    </span>
                  )}
                </div>
                <p className="text-blue-900 dark:text-blue-100 bg-blue-100 dark:bg-blue-800/30 p-3 rounded-lg">
                  {taskData.feedback_data.feedback}
                </p>
              </div>
            )}

            {/* Editable Feedback Input */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Your Feedback
              </label>
              <div className="relative">
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Enter your feedback about this task..."
                  rows={4}
                  className="w-full border-2 border-gray-300 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-vertical min-h-[100px] placeholder:text-gray-400"
                />
                <div className="absolute bottom-3 right-3 text-xs text-gray-400">
                  {feedback.length} characters
                </div>
              </div>
            </div>

            {/* Feedback Quick Options */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">Quick options:</span>
              {["great", "good", "needs improvement", "completed", "requires revision"].map((option) => (
                <button
                  key={option}
                  onClick={() => setFeedback(option)}
                  className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-orange-100 dark:hover:bg-orange-900/30 hover:text-orange-700 dark:hover:text-orange-300 transition-colors capitalize"
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        </div>



        {/* âœ… REPLACE your existing Download button with this */}
        <Button
          onClick={saveTaskData}
          disabled={saving || !taskData}
          className={`px-6 py-3 rounded-xl flex items-center gap-2 font-semibold shadow-lg transition-all duration-200 transform hover:scale-105 ${saving
            ? 'bg-gray-400 cursor-not-allowed'
            : saveSuccess
              ? 'bg-green-500 hover:bg-green-600'
              : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600'
            } text-white`}
        >
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : saveSuccess ? (
            <>
              <Check className="h-4 w-4" />
              Saved!
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              Save to Sheets
            </>
          )}
        </Button>


        {/* Footer */}
        <div className="bg-gray-100 dark:bg-gray-800 rounded-2xl p-6">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>Saved: {formatDate(taskData?.saved_at || taskData?.timestamp || '')}</span>
              {taskData?.submission_id && <span>ID: {taskData.submission_id.slice(0, 8)}...</span>}
            </div>
            {taskData?.bucket_path && (
              <code className="bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded text-xs">
                {taskData.bucket_name}/{taskData.bucket_path}
              </code>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}